* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color System */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;
    
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    --success-50: #f0fdf4;
    --success-500: #22c55e;
    --success-600: #16a34a;
    
    --warning-50: #fffbeb;
    --warning-500: #f59e0b;
    --warning-600: #d97706;
    
    --error-50: #fef2f2;
    --error-500: #ef4444;
    --error-600: #dc2626;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'SF Pro Display', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}



body {
    font-family: var(--font-family);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 50%, var(--gray-200) 100%);
    color: var(--gray-900);
    line-height: 1.6;
    min-height: 100vh;
    font-feature-settings: 'liga', 'kern';
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: var(--font-size-base);
}

header {
    background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 60%, var(--gray-700) 100%);
    color: white;
    padding: var(--space-10) var(--space-8) var(--space-8);
    box-shadow: var(--shadow-xl), 0 1px 0 rgba(255, 255, 255, 0.05) inset;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
}

header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.header-content {
    margin-bottom: var(--space-6);
    position: relative;
    z-index: 2;
}

header h1 {
    font-size: var(--font-size-4xl);
    font-weight: 900;
    margin-bottom: var(--space-2);
    background: linear-gradient(135deg, #ffffff 0%, var(--primary-200) 60%, var(--primary-300) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.025em;
    position: relative;
    z-index: 1;
    text-shadow: 0 0 40px rgba(59, 130, 246, 0.3);
}

.header-subtitle {
    color: var(--gray-300);
    font-size: var(--font-size-lg);
    font-weight: 500;
    opacity: 0.95;
    letter-spacing: 0.025em;
}



nav {
    display: flex;
    gap: var(--space-2);
    flex-wrap: wrap;
    position: relative;
    z-index: 2;
}

.nav-btn {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.12);
    color: var(--gray-200);
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-xl);
    cursor: pointer;
    transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    font-size: var(--font-size-sm);
    backdrop-filter: blur(16px);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    position: relative;
    z-index: 1;
    white-space: nowrap;
    user-select: none;
}

.nav-icon {
    font-size: var(--font-size-lg);
    transition: transform var(--transition-fast) ease;
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.2));
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), 0 0 20px rgba(59, 130, 246, 0.2);
    color: white;
}

.nav-btn:hover .nav-icon {
    transform: scale(1.15) rotate(5deg);
    filter: drop-shadow(0 0 12px rgba(255, 255, 255, 0.4));
}

.nav-btn.active {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(37, 99, 235, 0.15) 100%);
    border-color: rgba(59, 130, 246, 0.4);
    color: white;
    box-shadow: var(--shadow-md), 0 0 20px rgba(59, 130, 246, 0.3);
    transform: translateY(-1px);
}

.nav-btn.active .nav-icon {
    transform: scale(1.1);
    filter: drop-shadow(0 0 12px rgba(59, 130, 246, 0.6));
}
    color: #ffffff;
    box-shadow: 0 4px 20px rgba(255, 255, 255, 0.12), 0 1px 0 rgba(255, 255, 255, 0.2) inset;
    transform: translateY(-1px);
}

main {
    max-width: 1200px;
    margin: var(--space-16) auto;
    padding: 0 var(--space-8);
    min-height: calc(100vh - 200px);
}

.page {
    display: none;
    opacity: 0;
    transform: translateY(20px);
    transition: all var(--transition-normal) ease-out;
}

.page.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
    animation: fadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
    from { 
        opacity: 0; 
        transform: translateY(20px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

@keyframes pulse {
    0% { 
        transform: scale(1); 
        box-shadow: var(--shadow-md);
    }
    50% { 
        transform: scale(1.02); 
        box-shadow: var(--shadow-lg), 0 0 20px rgba(59, 130, 246, 0.3);
    }
    100% { 
        transform: scale(1); 
        box-shadow: var(--shadow-md);
    }
}

@keyframes spin {
    from { 
        transform: rotate(0deg); 
    }
    to { 
        transform: rotate(360deg); 
    }
}

.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
}

.primary-btn.loading {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 80%, var(--primary-800) 100%);
    cursor: not-allowed;
    transform: none;
}

.primary-btn.loading:hover {
    transform: none;
    box-shadow: var(--shadow-lg), 0 0 0 1px rgba(59, 130, 246, 0.1), 0 0 20px rgba(59, 130, 246, 0.2);
}

/* Toast Notifications */
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    padding: var(--space-4) var(--space-5);
    box-shadow: var(--shadow-xl);
    z-index: 10000;
    transform: translateX(400px);
    opacity: 0;
    transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
    max-width: 400px;
    min-width: 300px;
}

.toast-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.toast-content {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.toast-icon {
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.toast-message {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-900);
    line-height: 1.4;
}

.toast-close {
    position: absolute;
    top: var(--space-2);
    right: var(--space-2);
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--gray-500);
    cursor: pointer;
    padding: var(--space-1);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    line-height: 1;
}

.toast-close:hover {
    background: rgba(0, 0, 0, 0.05);
    color: var(--gray-700);
}

.toast-success {
    border-left: 4px solid var(--success-500);
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.toast-error {
    border-left: 4px solid var(--error-500);
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.toast-warning {
    border-left: 4px solid var(--warning-500);
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.toast-info {
    border-left: 4px solid var(--primary-500);
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
}

.page-header {
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
    padding: 2rem 0;
}

#randomPage .page-header {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(139, 92, 246, 0.03));
    border-radius: 24px;
    margin-bottom: 3rem;
    border: 1px solid rgba(59, 130, 246, 0.1);
}

h2 {
    color: #0f172a;
    margin-bottom: 1rem;
    font-size: 2.25rem;
    font-weight: 800;
    letter-spacing: -0.03em;
    line-height: 1.2;
}

.page-description {
    color: #64748b;
    font-size: 1.125rem;
    font-weight: 400;
    max-width: 650px;
    margin: 0 auto;
    line-height: 1.7;
}

h3 {
    color: #334155;
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.form-container, .admin-container {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(32px);
    padding: var(--space-12);
    border-radius: var(--radius-2xl);
    box-shadow: 
        var(--shadow-xl),
        0 0 0 1px rgba(255, 255, 255, 0.2),
        0 0 0 1px rgba(59, 130, 246, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-normal) ease;
}

.form-container:hover, .admin-container:hover {
    transform: translateY(-4px);
    box-shadow: 
        var(--shadow-xl),
        0 0 0 1px rgba(59, 130, 246, 0.1),
        0 0 40px rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.2);
}

.form-container::before, .admin-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(59, 130, 246, 0.02) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(236, 72, 153, 0.02) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

.form-container > *, .admin-container > * {
    position: relative;
    z-index: 1;
}

.input-group {
    margin-bottom: 2.5rem;
}

.input-group:last-child {
    margin-bottom: 0;
}

label {
    display: block;
    margin-bottom: 0.875rem;
    font-weight: 700;
    color: #374151;
    font-size: 0.95rem;
    letter-spacing: 0.025em;
    text-transform: uppercase;
    font-size: 0.85rem;
}

input[type="text"], select, textarea {
    width: 100%;
    padding: var(--space-4) var(--space-5);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-base);
    transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(12px);
    font-weight: 500;
    color: var(--gray-900);
    box-shadow: var(--shadow-sm);
}

input[type="text"]:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 
        0 0 0 4px rgba(59, 130, 246, 0.15), 
        var(--shadow-md),
        0 0 0 1px rgba(59, 130, 246, 0.1);
    background: rgba(255, 255, 255, 0.98);
    transform: translateY(-2px);
}

input[type="text"]:hover, select:hover, textarea:hover {
    border-color: var(--primary-300);
    box-shadow: var(--shadow-md);
}
}

textarea {
    min-height: 180px;
    resize: vertical;
    font-family: 'JetBrains Mono', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Compact textarea for generator cards */
.generator-card textarea {
    min-height: 60px;
    font-size: 0.85rem;
    transition: height var(--transition-fast);
    overflow: hidden;
}

#sealedSecretOutput {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-color: #e2e8f0;
}

.primary-btn {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 80%, var(--primary-700) 100%);
    color: white;
    border: none;
    padding: var(--space-4) var(--space-10);
    border-radius: var(--radius-xl);
    cursor: pointer;
    font-size: var(--font-size-base);
    font-weight: 700;
    transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 
        var(--shadow-lg),
        0 0 0 1px rgba(59, 130, 246, 0.1),
        0 0 20px rgba(59, 130, 246, 0.2);
    min-width: 180px;
    position: relative;
    overflow: hidden;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.primary-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.primary-btn:hover::before {
    left: 100%;
}

.primary-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4), 0 4px 16px rgba(59, 130, 246, 0.25);
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 80%, #1e40af 100%);
}

.primary-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.35);
}

.primary-btn:disabled {
    background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.primary-btn:disabled::before {
    display: none;
}

.copy-btn {
    position: absolute;
    top: 3rem;
    right: 1rem;
    background: linear-gradient(135deg, #10b981 0%, #059669 80%, #047857 100%);
    color: white;
    border: none;
    padding: 0.625rem 1.25rem;
    border-radius: 14px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 3px 12px rgba(16, 185, 129, 0.3), 0 1px 4px rgba(16, 185, 129, 0.2);
    z-index: 10;
}

.copy-btn:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 80%, #065f46 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4), 0 2px 8px rgba(16, 185, 129, 0.25);
}

/* Settings Page Layout */
.settings-layout {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

/* Compact Settings Layout */
.settings-compact-layout {
    display: flex;
    gap: var(--space-6);
    max-width: 100%;
}

.settings-card {
    flex: 1;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(32px);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: all var(--transition-normal);
    min-height: 500px;
    display: flex;
    flex-direction: column;
}

.settings-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(32px);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all var(--transition-normal);
    min-height: 400px;
    display: flex;
    flex-direction: column;
}



.settings-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.card-header {
    margin-bottom: var(--space-6);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.card-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.card-header p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    margin: 0;
}

.compact-form {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    height: 100%;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.form-group label {
    font-weight: 600;
    color: var(--gray-700);
    font-size: var(--font-size-sm);
}

.form-group input,
.form-group textarea {
    padding: var(--space-3) var(--space-4);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    transition: all var(--transition-normal);
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
}

.form-group small {
    color: var(--gray-500);
    font-size: var(--font-size-xs);
}

.method-tabs {
    display: flex;
    gap: var(--space-2);
    background: var(--gray-100);
    padding: var(--space-1);
    border-radius: var(--radius-lg);
}

.method-tab {
    flex: 1;
    padding: var(--space-2) var(--space-3);
    border: none;
    background: transparent;
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.method-tab.active {
    background: white;
    box-shadow: var(--shadow-sm);
    color: var(--primary-600);
}

.method-tab:hover:not(.active) {
    background: rgba(255, 255, 255, 0.5);
}

.file-drop-zone {
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    cursor: pointer;
}

.file-drop-zone:hover {
    border-color: var(--primary-400);
    background: rgba(59, 130, 246, 0.05);
}

.file-drop-zone input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.drop-zone-content {
    pointer-events: none;
}

.drop-zone-icon {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--space-2);
}

.drop-zone-text {
    color: var(--gray-600);
}

.drop-zone-text strong {
    color: var(--gray-900);
    display: block;
    margin-bottom: var(--space-1);
}

.drop-zone-text small {
    color: var(--gray-500);
    font-size: var(--font-size-xs);
}

.clusters-compact-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    height: 100%;
    justify-content: center;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: var(--space-8);
    color: var(--gray-500);
    height: 100%;
}

.empty-state-icon {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--space-4);
    opacity: 0.3;
    background: var(--gray-100);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.empty-state-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--space-2);
}

.empty-state-description {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    max-width: 250px;
    line-height: 1.5;
}

.cluster-compact-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-3) var(--space-4);
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
}

.cluster-compact-item:hover {
    background: white;
    box-shadow: var(--shadow-md);
    border-color: var(--primary-200);
}

.cluster-compact-info {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.cluster-compact-avatar {
    width: 32px;
    height: 32px;
    background: var(--primary-100);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    color: var(--primary-600);
    font-weight: 600;
}

.cluster-compact-details h4 {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.cluster-compact-details p {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    margin: 0;
}

.cluster-compact-actions {
    display: flex;
    gap: var(--space-2);
}

.cluster-compact-actions button {
    padding: var(--space-1) var(--space-2);
    border: none;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.cluster-compact-actions .edit-btn {
    background: var(--primary-100);
    color: var(--primary-600);
}

.cluster-compact-actions .edit-btn:hover {
    background: var(--primary-200);
}

.cluster-compact-actions .delete-btn {
    background: var(--error-50);
    color: var(--error-600);
}

.cluster-compact-actions .delete-btn:hover {
    background: var(--error-100);
}

/* Editing and deleting states */
.cluster-compact-item.editing {
    background: var(--primary-50);
    border-color: var(--primary-300);
    box-shadow: var(--shadow-md);
}

.cluster-compact-item.deleting {
    background: var(--error-50);
    border-color: var(--error-300);
    box-shadow: var(--shadow-md);
}

/* Edit and delete action buttons */
.save-btn, .cancel-btn, .confirm-delete-btn, .cancel-delete-btn {
    padding: var(--space-1) var(--space-2);
    border: none;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.save-btn {
    background: var(--success-50);
    color: var(--success-600);
}

.save-btn:hover {
    background: var(--success-100);
}

.cancel-btn {
    background: var(--gray-100);
    color: var(--gray-600);
}

.cancel-btn:hover {
    background: var(--gray-200);
}

.confirm-delete-btn {
    background: var(--error-50);
    color: var(--error-600);
}

.confirm-delete-btn:hover {
    background: var(--error-100);
}

.cancel-delete-btn {
    background: var(--gray-100);
    color: var(--gray-600);
}

.cancel-delete-btn:hover {
    background: var(--gray-200);
}

@media (max-width: 1024px) {
    .settings-compact-layout {
        flex-direction: column;
    }
}

.settings-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.08),
        0 8px 24px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.section-header {
    padding: 2.5rem 3rem 1.5rem;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.8) 100%);
    border-bottom: 1px solid rgba(226, 232, 240, 0.3);
}

.section-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.75rem;
}

.section-icon {
    font-size: 1.5rem;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.section-title h3 {
    color: #0f172a;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.section-description {
    color: #64748b;
    font-size: 1rem;
    line-height: 1.5;
    margin: 0;
}

.section-content {
    padding: 3rem;
}

/* Form Styling */
.cluster-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.form-row {
    display: flex;
    gap: 2rem;
}

.form-field {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.form-field.full-width {
    flex: none;
    width: 100%;
}

.field-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #374151;
    font-size: 0.95rem;
}

.label-text {
    color: #374151;
}

.label-required {
    color: #ef4444;
    font-weight: 700;
}

.field-input, .field-textarea {
    padding: 1rem 1.25rem;
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(8px);
}

.field-input:focus, .field-textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
}

.field-input:hover, .field-textarea:hover {
    border-color: #cbd5e1;
}

.field-textarea {
    min-height: 120px;
    resize: none;
    font-family: 'JetBrains Mono', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
    overflow: hidden;
    transition: height var(--transition-fast);
}

.field-hint {
    color: #64748b;
    font-size: 0.85rem;
    line-height: 1.4;
}

/* Method Selector */
.method-selector {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    background: rgba(248, 250, 252, 0.6);
    padding: 0.75rem;
    border-radius: 16px;
    border: 1px solid rgba(226, 232, 240, 0.6);
}

.method-option {
    background: transparent;
    border: 2px solid transparent;
    border-radius: 12px;
    padding: 1.25rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 1rem;
    text-align: left;
    position: relative;
    overflow: hidden;
}

.method-option:hover {
    background: rgba(255, 255, 255, 0.8);
    border-color: rgba(59, 130, 246, 0.2);
    transform: translateY(-1px);
}

.method-option.active {
    background: rgba(255, 255, 255, 0.95);
    border-color: #3b82f6;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.15);
    transform: translateY(-1px);
}

.method-option.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.method-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.method-content {
    flex: 1;
}

.method-title {
    font-weight: 600;
    color: #0f172a;
    margin-bottom: 0.25rem;
}

.method-desc {
    color: #64748b;
    font-size: 0.85rem;
    line-height: 1.3;
}

/* File Upload */
.file-upload-area {
    position: relative;
    border: 2px dashed #cbd5e1;
    border-radius: 16px;
    background: rgba(248, 250, 252, 0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    overflow: hidden;
}

.file-upload-area:hover {
    border-color: #3b82f6;
    background: rgba(239, 246, 255, 0.8);
    transform: translateY(-1px);
}

.file-upload-area:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

.file-input {
    position: absolute;
    inset: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    z-index: 2;
}

.file-upload-content {
    padding: 3rem 2rem;
    text-align: center;
    pointer-events: none;
}

.file-upload-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.6;
}

.file-upload-primary {
    font-weight: 600;
    color: #0f172a;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.file-upload-secondary {
    color: #64748b;
    font-size: 0.9rem;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: flex-end;
    padding-top: 1rem;
    border-top: 1px solid rgba(226, 232, 240, 0.6);
}

/* Cluster Cards */
.clusters-grid {
    display: grid;
    gap: 1.5rem;
}

.cluster-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(8px);
    border: 2px solid rgba(226, 232, 240, 0.6);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.cluster-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
    border-color: rgba(59, 130, 246, 0.3);
}

.cluster-card.editing {
    border-color: #3b82f6;
    box-shadow: 0 12px 32px rgba(59, 130, 246, 0.2);
    transform: translateY(-2px);
}

.cluster-card.deleting {
    border-color: #ef4444;
    box-shadow: 0 12px 32px rgba(239, 68, 68, 0.2);
    transform: translateY(-2px);
}

.cluster-card-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1.5rem;
}

.cluster-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
    min-width: 0;
}

.cluster-avatar {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 10px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 2px solid rgba(226, 232, 240, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.cluster-avatar-icon {
    font-size: 1.1rem;
}

.cluster-details {
    flex: 1;
    min-width: 0;
}

.cluster-name-container {
    margin-bottom: 0.25rem;
}

.cluster-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #0f172a;
    margin: 0;
    word-break: break-word;
    cursor: default;
}

.cluster-name-input {
    font-size: 1.1rem;
    font-weight: 600;
    color: #0f172a;  
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid #3b82f6;
    border-radius: 8px;
    padding: 0.6rem 0.9rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cluster-name-input:not(:disabled) {
    width: 600px !important;
    min-width: 600px !important;
    max-width: none !important;
}

.cluster-name-input:disabled {
    background: transparent;
    border: 2px solid transparent;
    color: transparent;
    cursor: default;
    pointer-events: none;
    user-select: none;
}

.cluster-name-input:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
    background: rgba(255, 255, 255, 1);
}

.cluster-status {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.9rem;
    font-weight: 500;
}

.status-indicator {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    flex-shrink: 0;
}

.status-active .status-indicator {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

.status-warning .status-indicator {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
}

.status-active .status-text {
    color: #059669;
}

.status-warning .status-text {
    color: #d97706;
}

.cluster-actions {
    position: relative;
    flex-shrink: 0;
}

.action-group {
    display: flex;
    gap: 0.75rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-group.hidden {
    opacity: 0;
    transform: translateX(10px);
    pointer-events: none;
    position: absolute;
    top: 0;
    right: 0;
    white-space: nowrap;
}

.action-group:not(.hidden) {
    opacity: 1;
    transform: translateX(0);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #64748b;
}

.empty-state-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.6;
}

.empty-state-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.75rem;
}

.empty-state-description {
    font-size: 1rem;
    line-height: 1.5;
    max-width: 400px;
    margin: 0 auto;
}

/* Button System */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 12px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    position: relative;
    overflow: hidden;
    min-width: 120px;
}

.btn:disabled {
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    min-width: 90px;
}

.btn-icon {
    font-size: 1rem;
}

/* Button Variants */
.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
}

.btn-primary:disabled {
    background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
}

.btn-secondary {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.25);
}

.btn-secondary:hover:not(:disabled) {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.35);
}

.btn-secondary:disabled {
    background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.25);
}

.btn-success:hover:not(:disabled) {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.35);
}

.btn-success:disabled {
    background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.25);
}

.btn-danger:hover:not(:disabled) {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.35);
}

.btn-danger:disabled {
    background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
}

.btn-danger.confirm-delete {
    animation: pulse-danger 2s infinite;
}

.btn-danger.confirm-delete:hover:not(:disabled) {
    animation: none;
}

.btn-ghost {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(107, 114, 128, 0.25);
}

.btn-ghost:hover:not(:disabled) {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.35);
}

.btn-ghost:disabled {
    background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
}

.base64-tools {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.tool-section {
    background: rgba(248, 250, 252, 0.8);
    backdrop-filter: blur(8px);
    padding: 2rem;
    border-radius: 20px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
}

.add-cluster-form, .clusters-list {
    background: rgba(248, 250, 252, 0.8);
    backdrop-filter: blur(8px);
    padding: 2rem;
    border-radius: 20px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
}

.cluster-item {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(8px);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: 16px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cluster-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    border-color: rgba(59, 130, 246, 0.2);
}

.cluster-item.editing {
    border-color: rgba(59, 130, 246, 0.4);
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.15);
    transform: translateY(-2px);
}

.cluster-item.deleting {
    border-color: rgba(239, 68, 68, 0.4);
    box-shadow: 0 8px 24px rgba(239, 68, 68, 0.15);
    transform: translateY(-2px);
}

.cluster-name-container {
    position: relative;
}

.cluster-name {
    color: #0f172a;
    margin-bottom: 0.5rem;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cluster-name-input {
    font-size: 1.1rem;
    font-weight: 600;
    color: #0f172a;
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid #3b82f6;
    border-radius: 8px;
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.5rem;
    width: 100%;
    max-width: 300px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cluster-name-input:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: rgba(255, 255, 255, 1);
}

.cluster-status {
    color: #64748b;
    font-size: 0.9rem;
}

.cluster-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    min-width: 200px;
}

.normal-actions, .edit-actions, .delete-actions {
    display: flex;
    gap: 0.75rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.edit-actions, .delete-actions {
    opacity: 0;
    transform: translateX(10px);
}

.edit-actions:not(.hidden), .delete-actions:not(.hidden) {
    opacity: 1;
    transform: translateX(0);
}

.normal-actions.hidden {
    opacity: 0;
    transform: translateX(-10px);
}

.edit-btn, .delete-btn, .save-btn, .cancel-btn, .confirm-delete-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 80px;
    justify-content: center;
}

.btn-icon {
    font-size: 0.9rem;
}

.edit-btn {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.25);
}

.edit-btn:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.35);
}

.delete-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.25);
}

.delete-btn:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.35);
}

.save-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.25);
}

.save-btn:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.35);
}

.save-btn:disabled {
    background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.cancel-btn {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(107, 114, 128, 0.25);
}

.cancel-btn:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.35);
}

.confirm-delete-btn {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.25);
    animation: pulse-danger 2s infinite;
}

.confirm-delete-btn:hover {
    background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.35);
    animation: none;
}

.confirm-delete-btn:disabled {
    background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    animation: none;
}

@keyframes pulse-danger {
    0%, 100% {
        box-shadow: 0 2px 8px rgba(220, 38, 38, 0.25);
    }
    50% {
        box-shadow: 0 4px 16px rgba(220, 38, 38, 0.4);
    }
}

.no-clusters {
    color: #64748b;
    font-style: italic;
    text-align: center;
    padding: 2rem;
    background: rgba(248, 250, 252, 0.6);
    border-radius: 12px;
    border: 1px dashed #cbd5e1;
}

.success-message {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    color: #14532d;
    padding: 1rem 1.25rem;
    border-radius: 16px;
    margin-bottom: 1.5rem;
    border: 1px solid #86efac;
    box-shadow: 0 4px 16px rgba(34, 197, 94, 0.1);
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.error-message {
    background: linear-gradient(135deg, #fecaca 0%, #fca5a5 100%);
    color: #7f1d1d;
    padding: 1rem 1.25rem;
    border-radius: 16px;
    margin-bottom: 1.5rem;
    border: 1px solid #f87171;
    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.1);
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

input[type="file"] {
    padding: 1rem;
    border: 2px dashed #cbd5e1;
    border-radius: 16px;
    background: rgba(248, 250, 252, 0.8);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.95rem;
}

input[type="file"]:hover {
    border-color: #3b82f6;
    background: rgba(239, 246, 255, 0.8);
    transform: translateY(-1px);
}

input[type="file"]:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

small {
    color: #64748b;
    font-size: 0.85rem;
    margin-top: 0.5rem;
    display: block;
    line-height: 1.4;
}

.cert-method-toggle {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.75rem;
    background: rgba(248, 250, 252, 0.6);
    padding: 0.5rem;
    border-radius: 12px;
    border: 1px solid rgba(226, 232, 240, 0.6);
}

.method-btn {
    background: transparent;
    border: none;
    color: #64748b;
    padding: 0.75rem 1.25rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex: 1;
    text-align: center;
}

.method-btn:hover {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.method-btn.active {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25);
}

.cert-input-method {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cert-input-method.hidden {
    display: none;
}

.textarea-with-button {
    position: relative;
}

.sample-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 10px;
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 10;
    box-shadow: 0 2px 8px rgba(6, 182, 212, 0.25);
}

.sample-btn:hover {
    background: linear-gradient(135deg, #0891b2 0%, #0e7490 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(6, 182, 212, 0.35);
}

@media (max-width: 768px) {
    .base64-tools {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    main {
        padding: 0 1rem;
        margin: 2rem auto;
    }
    
    header {
        padding: 1.5rem 1rem;
    }
    
    header h1 {
        font-size: 2rem;
        margin-bottom: 1rem;
    }
    
    nav {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .nav-btn {
        text-align: center;
    }
    
    .form-container {
        padding: 2rem 1.5rem;
        border-radius: 20px;
    }
    
    .tool-section {
        padding: 1.5rem;
    }
    
    /* Settings page mobile */
    .settings-layout {
        gap: 2rem;
    }
    
    .section-header {
        padding: 2rem 1.5rem 1rem;
    }
    
    .section-content {
        padding: 2rem 1.5rem;
    }
    
    .form-row {
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .method-selector {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .method-option {
        padding: 1rem;
    }
    
    .cluster-card {
        padding: 1.25rem;
    }
    
    .cluster-card-content {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .cluster-info {
        justify-content: center;
        text-align: center;
    }
    
    .cluster-details {
        text-align: center;
    }
    
    .cluster-actions {
        align-self: center;
    }
    
    .action-group {
        flex-wrap: wrap;
        gap: 0.75rem;
    }
    
    .btn {
        flex: 1;
        min-width: 100px;
    }
    
    .file-upload-content {
        padding: 2rem 1rem;
    }
    
    .file-upload-icon {
        font-size: 2.5rem;
    }
}

/* Professional Transform Direction Selector */
.transform-direction-selector {
    margin: 2rem 0;
    padding: 0;
}

.selector-header {
    text-align: center;
    margin-bottom: 2rem;
}

.selector-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.selector-header p {
    color: #64748b;
    font-size: 1rem;
    font-weight: 400;
}

.selector-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    max-width: 1000px;
    margin: 0 auto;
}

@media (max-width: 768px) {
    .selector-options {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

.transform-option {
    display: flex;
    align-items: flex-start;
    gap: 1.25rem;
    padding: 1.75rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 2px solid rgba(226, 232, 240, 0.8);
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    text-align: left;
    font-family: inherit;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1);
}

.transform-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(147, 51, 234, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.transform-option:hover {
    border-color: rgba(59, 130, 246, 0.4);
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1), 0 1px 3px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

.transform-option:hover::before {
    opacity: 1;
}

.transform-option.active {
    border-color: #3b82f6;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.15), 0 1px 3px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.transform-option.active::before {
    opacity: 1;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
}

.option-icon {
    flex-shrink: 0;
    margin-top: 0.25rem;
}

.icon-container {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.icon-container.decode {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border: 1px solid #93c5fd;
}

.icon-container.encode {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border: 1px solid #fbbf24;
}

.transform-option.active .icon-container.decode {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border-color: #1d4ed8;
    color: white;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
}

.transform-option.active .icon-container.encode {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    border-color: #b45309;
    color: white;
    box-shadow: 0 4px 16px rgba(245, 158, 11, 0.4);
}

.icon-symbol {
    display: block;
    filter: grayscale(0.3);
    transition: filter 0.3s ease;
}

.transform-option.active .icon-symbol {
    filter: grayscale(0);
}

.option-content {
    flex: 1;
    min-width: 0;
}

.option-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
    font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
}

.option-subtitle {
    font-size: 1rem;
    font-weight: 600;
    color: #475569;
    margin-bottom: 0.75rem;
}

.option-description {
    font-size: 0.9rem;
    color: #64748b;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.option-example {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.85rem;
    color: #475569;
    background: rgba(248, 250, 252, 0.8);
    padding: 0.75rem 1rem;
    border-radius: 8px;
    border: 1px solid rgba(226, 232, 240, 0.6);
    font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
}

.option-example code {
    background: rgba(59, 130, 246, 0.1);
    color: #1e40af;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: 600;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.transform-option.active .option-example code {
    background: rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.3);
}

.option-indicator {
    flex-shrink: 0;
    position: relative;
    margin-top: 0.25rem;
}

.indicator-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #e2e8f0;
    border: 2px solid #cbd5e1;
    transition: all 0.3s ease;
}

.transform-option.active .indicator-dot {
    background: #3b82f6;
    border-color: #2563eb;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* Modern Generator Tools Styling */
.generator-tools {
    display: flex;
    flex-direction: column;
    gap: 2.5rem;
    max-width: 1200px;
    margin: 0 auto;
}







/* Generator Cards */
.generator-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    backdrop-filter: blur(20px);
    padding: 2.5rem;
    border-radius: 24px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.06),
        0 2px 8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.generator-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.generator-card:hover {
    transform: translateY(-4px);
    box-shadow:
        0 16px 48px rgba(0, 0, 0, 0.12),
        0 8px 24px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.generator-card:hover::before {
    opacity: 1;
}



/* Card Headers */
.card-header {
    display: flex;
    align-items: center;
    gap: 1.25rem;
    margin-bottom: 2.5rem;
    position: relative;
}

.card-icon {
    width: 56px;
    height: 56px;
    border-radius: 16px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(59, 130, 246, 0.08));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    border: 1px solid rgba(59, 130, 246, 0.25);
    box-shadow:
        0 4px 12px rgba(59, 130, 246, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
}

.generator-card:hover .card-icon {
    transform: scale(1.05);
    box-shadow:
        0 6px 16px rgba(59, 130, 246, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

.card-title h3 {
    color: #0f172a;
    font-size: 1.625rem;
    font-weight: 700;
    margin: 0 0 0.375rem 0;
    letter-spacing: -0.025em;
    background: linear-gradient(135deg, #0f172a, #334155);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.card-title p {
    color: #64748b;
    font-size: 1rem;
    margin: 0;
    font-weight: 500;
    line-height: 1.4;
}

/* Form Elements */
.card-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Form Elements */
.card-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.form-field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-field label {
    color: #374151;
    font-size: 0.9rem;
    font-weight: 600;
    letter-spacing: 0.025em;
}

.number-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.8);
    transition: all 0.2s ease;
    outline: none;
}

.number-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background: white;
}

.select-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.2s ease;
    outline: none;
    cursor: pointer;
}

.select-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Slider Styling */
.slider-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.slider-value {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 0.375rem 1rem;
    border-radius: 24px;
    font-size: 0.875rem;
    font-weight: 600;
    min-width: 48px;
    text-align: center;
    font-family: 'JetBrains Mono', 'Monaco', 'Menlo', monospace;
    box-shadow:
        0 2px 8px rgba(59, 130, 246, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.length-slider {
    width: 100%;
    height: 10px;
    border-radius: 6px;
    background: linear-gradient(90deg, #f1f5f9, #e2e8f0);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.length-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    cursor: pointer;
    box-shadow:
        0 4px 12px rgba(59, 130, 246, 0.3),
        0 2px 4px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid white;
}

.length-slider::-webkit-slider-thumb:hover {
    transform: scale(1.15);
    box-shadow:
        0 6px 16px rgba(59, 130, 246, 0.4),
        0 4px 8px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.length-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    transition: all 0.2s ease;
}

.length-slider::-moz-range-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.length-slider::-moz-range-track {
    height: 8px;
    border-radius: 4px;
    background: #e5e7eb;
    border: none;
}

.length-slider:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}









/* Toggle Switches */
.form-section {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
    margin-bottom: 2rem;
}

.section-label {
    color: #1f2937;
    font-size: 1rem;
    font-weight: 700;
    letter-spacing: 0.025em;
    background: linear-gradient(135deg, #1f2937, #374151);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.toggle-group {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 0.75rem;
}

.toggle-group.compact {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 1rem;
}





.toggle-option {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.625rem;
    cursor: pointer;
    padding: 1rem 1.25rem;
    border-radius: 16px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border: 1px solid rgba(226, 232, 240, 0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 0;
    position: relative;
    overflow: hidden;
}

.toggle-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(59, 130, 246, 0.02));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.toggle-option:hover {
    transform: translateY(-2px);
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow:
        0 4px 12px rgba(59, 130, 246, 0.1),
        0 2px 4px rgba(0, 0, 0, 0.05);
}

.toggle-option:hover::before {
    opacity: 1;
}

.toggle-option input[type="checkbox"] {
    display: none;
}

.toggle-switch {
    width: 42px;
    height: 24px;
    background: linear-gradient(145deg, #e5e7eb, #d1d5db);
    border-radius: 12px;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.1),
        0 1px 2px rgba(0, 0, 0, 0.05);
}

.toggle-switch::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: linear-gradient(145deg, #ffffff, #f8fafc);
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 2px 6px rgba(0, 0, 0, 0.15),
        0 1px 2px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.toggle-option input[type="checkbox"]:checked + .toggle-switch {
    background: linear-gradient(145deg, #3b82f6, #2563eb);
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.1),
        0 1px 2px rgba(59, 130, 246, 0.2);
}

.toggle-option input[type="checkbox"]:checked + .toggle-switch::after {
    transform: translateX(18px);
    background: linear-gradient(145deg, #ffffff, #f1f5f9);
}

.toggle-label {
    color: #374151;
    font-size: 0.85rem;
    font-weight: 600;
    flex: 1;
    text-align: center;
    font-family: 'JetBrains Mono', 'Monaco', 'Menlo', monospace;
    text-transform: none;
}

/* Generate Buttons */
.generate-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1.25rem 2.5rem;
    border: none;
    border-radius: 16px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    text-transform: none;
    letter-spacing: 0.025em;
}

.generate-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.generate-btn:hover::before {
    left: 100%;
}

.generate-btn.primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    box-shadow:
        0 6px 20px rgba(59, 130, 246, 0.3),
        0 2px 8px rgba(59, 130, 246, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.generate-btn.secondary {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    box-shadow:
        0 6px 20px rgba(139, 92, 246, 0.3),
        0 2px 8px rgba(139, 92, 246, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.generate-btn:hover {
    transform: translateY(-2px);
}

.generate-btn.primary:hover {
    box-shadow:
        0 8px 24px rgba(59, 130, 246, 0.4),
        0 4px 12px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.generate-btn.secondary:hover {
    box-shadow:
        0 8px 24px rgba(139, 92, 246, 0.4),
        0 4px 12px rgba(139, 92, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-icon {
    font-size: 1.1rem;
}

/* Output Section */
.output-section {
    margin-top: 0.5rem;
}

.output-wrapper {
    position: relative;
    display: flex;
    gap: 0.75rem;
    flex: 1;
}

.output-field {
    flex: 1;
    min-height: 120px;
    max-height: 240px;
    padding: 1.25rem;
    border: 2px solid #e5e7eb;
    border-radius: 16px;
    font-family: 'JetBrains Mono', 'Monaco', 'Menlo', monospace;
    font-size: 0.9rem;
    line-height: 1.6;
    background: linear-gradient(145deg, rgba(248, 250, 252, 0.8), rgba(255, 255, 255, 0.6));
    resize: vertical;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.05),
        0 1px 2px rgba(0, 0, 0, 0.05);
}

.output-field:focus {
    border-color: #3b82f6;
    box-shadow:
        0 0 0 4px rgba(59, 130, 246, 0.1),
        inset 0 2px 4px rgba(0, 0, 0, 0.05),
        0 2px 8px rgba(59, 130, 246, 0.1);
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    transform: translateY(-1px);
}

.copy-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem 1.25rem;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border: 2px solid #e5e7eb;
    border-radius: 16px;
    color: #374151;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    white-space: nowrap;
    height: fit-content;
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.copy-btn:hover {
    background: linear-gradient(145deg, #f8fafc, rgba(255, 255, 255, 0.9));
    border-color: #3b82f6;
    color: #3b82f6;
    box-shadow:
        0 4px 12px rgba(59, 130, 246, 0.15),
        0 2px 4px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
}

.copy-icon {
    font-size: 1.1rem;
}

/* Utility Cards Row */


.utilities-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-top: 1rem;
}



.utility-card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    backdrop-filter: blur(20px);
    padding: 2rem;
    border-radius: 24px;
    border: 1px solid rgba(226, 232, 240, 0.8);
    box-shadow:
        0 6px 24px rgba(0, 0, 0, 0.06),
        0 2px 8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.utility-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.3), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.utility-card:hover {
    transform: translateY(-4px);
    box-shadow:
        0 12px 36px rgba(0, 0, 0, 0.1),
        0 6px 16px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.utility-card:hover::before {
    opacity: 1;
}

.utility-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.utility-icon {
    width: 44px;
    height: 44px;
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.15), rgba(16, 185, 129, 0.08));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.4rem;
    border: 1px solid rgba(16, 185, 129, 0.25);
    box-shadow:
        0 3px 8px rgba(16, 185, 129, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
}

.utility-card:hover .utility-icon {
    transform: scale(1.05);
    box-shadow:
        0 4px 12px rgba(16, 185, 129, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

.utility-header h4 {
    color: #0f172a;
    font-size: 1.2rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, #0f172a, #334155);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.utility-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.range-inputs, .string-controls {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.range-input, .length-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 0.9rem;
    font-weight: 500;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
    box-shadow:
        inset 0 1px 3px rgba(0, 0, 0, 0.05),
        0 1px 2px rgba(0, 0, 0, 0.05);
}

.range-input:focus, .length-input:focus {
    border-color: #10b981;
    box-shadow:
        0 0 0 4px rgba(16, 185, 129, 0.1),
        inset 0 1px 3px rgba(0, 0, 0, 0.05),
        0 2px 8px rgba(16, 185, 129, 0.1);
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
    transform: translateY(-1px);
}

.range-divider {
    color: #6b7280;
    font-weight: 600;
    font-size: 0.9rem;
}

.type-select {
    flex: 2;
    padding: 0.6rem 0.8rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.2s ease;
    outline: none;
    cursor: pointer;
}

.type-select:focus {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.utility-btn {
    padding: 0.875rem 1.5rem;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    white-space: nowrap;
    box-shadow:
        0 4px 12px rgba(16, 185, 129, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.utility-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.utility-btn:hover::before {
    left: 100%;
}

.utility-btn:hover {
    background: linear-gradient(135deg, #059669, #047857);
    box-shadow:
        0 6px 16px rgba(16, 185, 129, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.utility-output {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.output-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-family: 'JetBrains Mono', 'Monaco', 'Menlo', monospace;
    font-size: 0.9rem;
    background: linear-gradient(145deg, rgba(248, 250, 252, 0.8), rgba(255, 255, 255, 0.6));
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
    box-shadow:
        inset 0 1px 3px rgba(0, 0, 0, 0.05),
        0 1px 2px rgba(0, 0, 0, 0.05);
}

.output-input:focus {
    border-color: #10b981;
    box-shadow:
        0 0 0 4px rgba(16, 185, 129, 0.1),
        inset 0 1px 3px rgba(0, 0, 0, 0.05),
        0 2px 8px rgba(16, 185, 129, 0.1);
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    transform: translateY(-1px);
}

.copy-btn-small {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8));
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    color: #374151;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.copy-btn-small:hover {
    background: linear-gradient(145deg, #f8fafc, rgba(255, 255, 255, 0.9));
    border-color: #10b981;
    color: #10b981;
    box-shadow:
        0 4px 12px rgba(16, 185, 129, 0.15),
        0 2px 4px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .generator-tools {
        gap: 1.5rem;
    }

    .generator-card {
        padding: 1.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .toggle-group {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }

    .toggle-group .toggle-option:last-child {
        grid-column: 1 / -1;
    }

    .utilities-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .range-inputs, .string-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .range-divider {
        text-align: center;
    }

    .output-wrapper {
        flex-direction: column;
    }

    .copy-btn {
        align-self: flex-start;
    }
}