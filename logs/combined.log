{"level":"info","message":"Sealed Secret Manager server running on port 3000","service":"sealed-secret-manager","timestamp":"2025-07-30T14:39:54.064Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealed-secret-manager","timestamp":"2025-07-30T14:39:54.073Z"}
{"level":"info","message":"Sealed Secret Manager server running on port 3000","service":"sealed-secret-manager","timestamp":"2025-07-30T14:43:44.238Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealed-secret-manager","timestamp":"2025-07-30T14:43:44.248Z"}
{"level":"info","message":"Cluster added: db-country-k8s-pdc-nonprod-green.hcnet.vn (method: content)","service":"sealed-secret-manager","timestamp":"2025-07-30T14:44:08.480Z"}
{"level":"info","message":"Cluster added: db-country-k8s-pdc-preprod-green.hcnet.vn (method: content)","service":"sealed-secret-manager","timestamp":"2025-07-30T14:44:47.498Z"}
{"level":"info","message":"Cluster added: db-country-k8s-pdc-prod-green.hcnet.vn (method: content)","service":"sealed-secret-manager","timestamp":"2025-07-30T14:45:17.929Z"}
{"level":"info","message":"Secret converted for cluster: db-country-k8s-pdc-nonprod-green.hcnet.vn","service":"sealed-secret-manager","timestamp":"2025-07-30T14:46:02.790Z"}
{"level":"info","message":"Secret converted for cluster: db-country-k8s-pdc-nonprod-green.hcnet.vn","service":"sealed-secret-manager","timestamp":"2025-07-30T14:46:18.523Z"}
{"level":"info","message":"Secret converted for cluster: db-country-k8s-pdc-nonprod-green.hcnet.vn","service":"sealed-secret-manager","timestamp":"2025-07-30T14:52:35.729Z"}
{"level":"info","message":"Secret converted for cluster: db-country-k8s-pdc-nonprod-green.hcnet.vn","service":"sealed-secret-manager","timestamp":"2025-07-30T14:52:43.654Z"}
{"level":"info","message":"Secret converted for cluster: db-country-k8s-pdc-nonprod-green.hcnet.vn","service":"sealed-secret-manager","timestamp":"2025-07-30T14:53:02.867Z"}
{"level":"info","message":"Secret converted for cluster: db-country-k8s-pdc-nonprod-green.hcnet.vn","service":"sealed-secret-manager","timestamp":"2025-07-30T14:53:08.929Z"}
{"level":"info","message":"Sealed Secret Manager server running on port 3000","service":"sealed-secret-manager","timestamp":"2025-07-30T14:54:53.214Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealed-secret-manager","timestamp":"2025-07-30T14:54:53.223Z"}
{"level":"info","message":"Convert request - clusterId: 2fa14f29-07c0-429c-a33e-97f7e9c8c1a1, targetNamespace: 'default2222'","service":"sealed-secret-manager","timestamp":"2025-07-30T14:55:09.863Z"}
{"level":"info","message":"Namespace resolution - from secret: 'undefined', targetNamespace: 'default2222', final: 'default2222'","service":"sealed-secret-manager","timestamp":"2025-07-30T14:55:09.879Z"}
{"level":"error","message":"Error converting secret: Assignment to constant variable.","service":"sealed-secret-manager","stack":"TypeError: Assignment to constant variable.\n    at /Users/<USER>/HC/Platform-Tools/chore-tasks/server.js:447:24\n    at Layer.handle [as handle_request] (/Users/<USER>/HC/Platform-Tools/chore-tasks/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/HC/Platform-Tools/chore-tasks/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/Users/<USER>/HC/Platform-Tools/chore-tasks/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/Users/<USER>/HC/Platform-Tools/chore-tasks/node_modules/express/lib/router/layer.js:95:5)\n    at /Users/<USER>/HC/Platform-Tools/chore-tasks/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/Users/<USER>/HC/Platform-Tools/chore-tasks/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/HC/Platform-Tools/chore-tasks/node_modules/express/lib/router/index.js:280:10)\n    at serveStatic (/Users/<USER>/HC/Platform-Tools/chore-tasks/node_modules/serve-static/index.js:75:16)\n    at Layer.handle [as handle_request] (/Users/<USER>/HC/Platform-Tools/chore-tasks/node_modules/express/lib/router/layer.js:95:5)","timestamp":"2025-07-30T14:55:09.880Z"}
{"level":"info","message":"Sealed Secret Manager server running on port 3000","service":"sealed-secret-manager","timestamp":"2025-07-30T14:55:46.757Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealed-secret-manager","timestamp":"2025-07-30T14:55:46.767Z"}
{"level":"info","message":"Sealed Secret Manager server running on port 3000","service":"sealed-secret-manager","timestamp":"2025-07-30T14:56:11.610Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealed-secret-manager","timestamp":"2025-07-30T14:56:11.619Z"}
{"level":"info","message":"Convert request - clusterId: 2fa14f29-07c0-429c-a33e-97f7e9c8c1a1, targetNamespace: 'default555'","service":"sealed-secret-manager","timestamp":"2025-07-30T14:56:22.087Z"}
{"level":"info","message":"Namespace resolution - from secret: 'undefined', targetNamespace: 'default555', final: 'default555'","service":"sealed-secret-manager","timestamp":"2025-07-30T14:56:22.102Z"}
{"level":"info","message":"Added namespace 'default555' to secret YAML","service":"sealed-secret-manager","timestamp":"2025-07-30T14:56:22.103Z"}
{"level":"info","message":"Secret converted for cluster: db-country-k8s-pdc-nonprod-green.hcnet.vn","service":"sealed-secret-manager","timestamp":"2025-07-30T14:56:22.382Z"}
{"level":"info","message":"Convert request - clusterId: beba9bf3-2d9b-4f0f-952f-6807a0683842, targetNamespace: 'default'","service":"sealed-secret-manager","timestamp":"2025-07-30T15:03:09.479Z"}
{"level":"info","message":"Namespace resolution - from secret: 'eee', targetNamespace: 'default', final: 'eee'","service":"sealed-secret-manager","timestamp":"2025-07-30T15:03:09.498Z"}
{"level":"info","message":"Secret converted for cluster: db-country-k8s-pdc-preprod-green.hcnet.vn","service":"sealed-secret-manager","timestamp":"2025-07-30T15:03:09.776Z"}
{"level":"info","message":"Convert request - clusterId: e058b8ce-d317-425f-a2ad-f72b9db1be92, targetNamespace: 'default'","service":"sealed-secret-manager","timestamp":"2025-07-30T15:05:50.196Z"}
{"level":"info","message":"Namespace resolution - from secret: 'eee', targetNamespace: 'default', final: 'eee'","service":"sealed-secret-manager","timestamp":"2025-07-30T15:05:50.210Z"}
{"level":"info","message":"Secret converted for cluster: db-country-k8s-pdc-prod-green.hcnet.vn","service":"sealed-secret-manager","timestamp":"2025-07-30T15:05:50.277Z"}
{"level":"info","message":"Convert request - clusterId: beba9bf3-2d9b-4f0f-952f-6807a0683842, targetNamespace: 'default'","service":"sealed-secret-manager","timestamp":"2025-07-30T15:06:56.104Z"}
{"level":"info","message":"Namespace resolution - from secret: 'eee', targetNamespace: 'default', final: 'eee'","service":"sealed-secret-manager","timestamp":"2025-07-30T15:06:56.119Z"}
{"level":"info","message":"Secret converted for cluster: db-country-k8s-pdc-preprod-green.hcnet.vn","service":"sealed-secret-manager","timestamp":"2025-07-30T15:06:56.184Z"}
{"level":"info","message":"Convert request - clusterId: 2fa14f29-07c0-429c-a33e-97f7e9c8c1a1, targetNamespace: 'default'","service":"sealed-secret-manager","timestamp":"2025-07-30T15:08:11.398Z"}
{"level":"info","message":"Namespace resolution - from secret: 'eee', targetNamespace: 'default', final: 'eee'","service":"sealed-secret-manager","timestamp":"2025-07-30T15:08:11.409Z"}
{"level":"info","message":"Secret converted for cluster: db-country-k8s-pdc-nonprod-green.hcnet.vn","service":"sealed-secret-manager","timestamp":"2025-07-30T15:08:11.683Z"}
{"level":"info","message":"Convert request - clusterId: 2fa14f29-07c0-429c-a33e-97f7e9c8c1a1, targetNamespace: 'default'","service":"sealed-secret-manager","timestamp":"2025-07-30T15:10:14.180Z"}
{"level":"info","message":"Namespace resolution - from secret: 'eee', targetNamespace: 'default', final: 'eee'","service":"sealed-secret-manager","timestamp":"2025-07-30T15:10:14.197Z"}
{"level":"info","message":"Secret converted for cluster: db-country-k8s-pdc-nonprod-green.hcnet.vn","service":"sealed-secret-manager","timestamp":"2025-07-30T15:10:14.261Z"}
{"level":"info","message":"Convert request - clusterId: 2fa14f29-07c0-429c-a33e-97f7e9c8c1a1, targetNamespace: 'default'","service":"sealed-secret-manager","timestamp":"2025-07-30T15:11:34.997Z"}
{"level":"info","message":"Namespace resolution - from secret: 'eee', targetNamespace: 'default', final: 'eee'","service":"sealed-secret-manager","timestamp":"2025-07-30T15:11:35.005Z"}
{"level":"info","message":"Secret converted for cluster: db-country-k8s-pdc-nonprod-green.hcnet.vn","service":"sealed-secret-manager","timestamp":"2025-07-30T15:11:35.064Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-30T15:14:00.401Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-30T15:14:00.411Z"}
{"level":"info","message":"Cluster updated: db-country-k8s-pdc-nonprod-green.hcnet.v (method: name-only)","service":"sealify","timestamp":"2025-07-30T15:15:04.040Z"}
{"level":"info","message":"Cluster updated: db-country-k8s-pdc-nonprod-green.hcnet.vn (method: name-only)","service":"sealify","timestamp":"2025-07-30T15:15:14.488Z"}
{"level":"info","message":"Convert request - clusterId: 2fa14f29-07c0-429c-a33e-97f7e9c8c1a1, targetNamespace: 'default'","service":"sealify","timestamp":"2025-07-30T15:28:09.040Z"}
{"level":"info","message":"Namespace resolution - from secret: 'eee', targetNamespace: 'default', final: 'eee'","service":"sealify","timestamp":"2025-07-30T15:28:09.052Z"}
{"level":"info","message":"Cleaned sealed secret YAML (removed creationTimestamp)","service":"sealify","timestamp":"2025-07-30T15:28:09.334Z"}
{"level":"info","message":"Secret converted for cluster: db-country-k8s-pdc-nonprod-green.hcnet.vn","service":"sealify","timestamp":"2025-07-30T15:28:09.334Z"}
{"level":"info","message":"Convert request - clusterId: 2fa14f29-07c0-429c-a33e-97f7e9c8c1a1, targetNamespace: 'default'","service":"sealify","timestamp":"2025-07-30T15:28:25.459Z"}
{"level":"info","message":"Namespace resolution - from secret: 'eee', targetNamespace: 'default', final: 'eee'","service":"sealify","timestamp":"2025-07-30T15:28:25.475Z"}
{"level":"info","message":"Cleaned sealed secret YAML (removed creationTimestamp)","service":"sealify","timestamp":"2025-07-30T15:28:25.577Z"}
{"level":"info","message":"Secret converted for cluster: db-country-k8s-pdc-nonprod-green.hcnet.vn","service":"sealify","timestamp":"2025-07-30T15:28:25.577Z"}
{"level":"info","message":"Convert request - clusterId: 2fa14f29-07c0-429c-a33e-97f7e9c8c1a1, targetNamespace: 'default'","service":"sealify","timestamp":"2025-07-30T15:29:16.834Z"}
{"level":"info","message":"Namespace resolution - from secret: 'eee', targetNamespace: 'default', final: 'eee'","service":"sealify","timestamp":"2025-07-30T15:29:16.850Z"}
{"level":"info","message":"Cleaned sealed secret YAML (removed creationTimestamp)","service":"sealify","timestamp":"2025-07-30T15:29:16.909Z"}
{"level":"info","message":"Secret converted for cluster: db-country-k8s-pdc-nonprod-green.hcnet.vn","service":"sealify","timestamp":"2025-07-30T15:29:16.909Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-30T15:30:31.260Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-30T15:30:31.270Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-30T15:30:57.665Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-30T15:30:57.675Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-30T15:33:45.347Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-30T15:33:45.355Z"}
{"level":"info","message":"Convert request - clusterId: beba9bf3-2d9b-4f0f-952f-6807a0683842, targetNamespace: 'default'","service":"sealify","timestamp":"2025-07-30T15:51:28.967Z"}
{"level":"info","message":"Namespace resolution - from secret: 'undefined', targetNamespace: 'default', final: 'default'","service":"sealify","timestamp":"2025-07-30T15:51:28.977Z"}
{"level":"info","message":"Added namespace 'default' to secret YAML","service":"sealify","timestamp":"2025-07-30T15:51:28.978Z"}
{"level":"info","message":"Cleaned sealed secret YAML (removed creationTimestamp)","service":"sealify","timestamp":"2025-07-30T15:51:29.261Z"}
{"level":"info","message":"Secret converted for cluster: db-country-k8s-pdc-preprod-green.hcnet.vn","service":"sealify","timestamp":"2025-07-30T15:51:29.261Z"}
{"level":"info","message":"Cluster added: tools-country-k8s-pdc-green.hcnet.vn (method: content)","service":"sealify","timestamp":"2025-07-30T15:53:37.817Z"}
{"level":"info","message":"Cluster added: tools-country-k8s-drc-green.hcnet.vn (method: content)","service":"sealify","timestamp":"2025-07-30T15:54:17.813Z"}
{"level":"info","message":"Cluster added: apps-country-k8s-pdc-prod-green.hcnet.vn (method: content)","service":"sealify","timestamp":"2025-07-30T15:54:55.076Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-30T15:59:05.422Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-30T15:59:05.432Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-30T15:59:05.440Z"}
{"level":"info","message":"Note created with ID: bb78e591-8282-4025-aa4c-e26fd24253a5, expires at: 2025-07-30T16:59:12.677Z","service":"sealify","timestamp":"2025-07-30T15:59:12.677Z"}
{"level":"info","message":"Note accessed: bb78e591-8282-4025-aa4c-e26fd24253a5, will be deleted on next cleanup","service":"sealify","timestamp":"2025-07-30T15:59:32.253Z"}
{"level":"info","message":"Cleaned 1 notes (expired or accessed)","service":"sealify","timestamp":"2025-07-30T15:59:34.903Z"}
{"level":"info","message":"Note created with ID: 84adb728-4cce-4e17-9633-34d55c713f9a, expires at: 2025-07-30T16:59:48.084Z","service":"sealify","timestamp":"2025-07-30T15:59:48.085Z"}
{"level":"info","message":"Note accessed: 84adb728-4cce-4e17-9633-34d55c713f9a, will be deleted on next cleanup","service":"sealify","timestamp":"2025-07-30T15:59:53.988Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-30T16:01:29.513Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-30T16:01:29.522Z"}
{"level":"info","message":"Cleaned 1 notes (expired or accessed)","service":"sealify","timestamp":"2025-07-30T16:01:29.530Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-30T16:01:29.531Z"}
{"level":"info","message":"Note created with ID: 71a7e321-52f8-44ae-9baa-5a81418c6cd1, expires at: 2025-07-31T16:03:42.930Z","service":"sealify","timestamp":"2025-07-30T16:03:42.930Z"}
{"level":"info","message":"Note accessed: 71a7e321-52f8-44ae-9baa-5a81418c6cd1, will be deleted on next cleanup","service":"sealify","timestamp":"2025-07-30T16:03:50.457Z"}
{"level":"info","message":"Cleaned 1 notes (expired or accessed)","service":"sealify","timestamp":"2025-07-30T16:03:53.845Z"}
{"level":"info","message":"Note created with ID: e13324b4-1b7c-4e85-bf83-0a66b39bae7b, expires at: 2025-07-31T16:10:12.626Z","service":"sealify","timestamp":"2025-07-30T16:10:12.627Z"}
{"level":"info","message":"Note accessed: e13324b4-1b7c-4e85-bf83-0a66b39bae7b, will be deleted on next cleanup","service":"sealify","timestamp":"2025-07-30T16:10:27.134Z"}
{"level":"info","message":"Cleaned 1 notes (expired or accessed)","service":"sealify","timestamp":"2025-07-30T16:10:43.022Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-31T02:17:33.639Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-31T02:17:33.648Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-31T02:17:33.656Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-31T02:18:01.896Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-31T02:18:01.905Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-31T02:18:01.913Z"}
{"level":"info","message":"Secret transformed from data to stringData","service":"sealify","timestamp":"2025-07-31T02:18:09.043Z"}
{"level":"info","message":"Secret transformed from stringData to data","service":"sealify","timestamp":"2025-07-31T02:18:18.557Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-31T02:20:05.476Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-31T02:20:05.507Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-31T02:20:05.515Z"}
{"level":"info","message":"Convert request - clusterId: d85c5580-31de-413c-a9d0-d90e1aa9844a, targetNamespace: 'default'","service":"sealify","timestamp":"2025-07-31T02:22:11.269Z"}
{"level":"info","message":"Namespace resolution - from secret: 'sealify', targetNamespace: 'default', final: 'sealify'","service":"sealify","timestamp":"2025-07-31T02:22:11.300Z"}
{"level":"info","message":"Cleaned sealed secret YAML (removed creationTimestamp)","service":"sealify","timestamp":"2025-07-31T02:22:11.611Z"}
{"level":"info","message":"Secret converted for cluster: tools-country-k8s-pdc-green.hcnet.vn","service":"sealify","timestamp":"2025-07-31T02:22:11.611Z"}
{"level":"info","message":"Note created with ID: 724de581-260c-45a3-a9c3-a4dab1b31189, expires at: 2025-08-01T02:22:43.477Z","service":"sealify","timestamp":"2025-07-31T02:22:43.478Z"}
{"level":"info","message":"Note accessed: 724de581-260c-45a3-a9c3-a4dab1b31189, will be deleted on next cleanup","service":"sealify","timestamp":"2025-07-31T02:22:48.147Z"}
{"level":"info","message":"Cleaned 1 notes (expired or accessed)","service":"sealify","timestamp":"2025-07-31T02:22:52.393Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-31T02:23:38.621Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-31T02:23:38.630Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-31T02:23:38.637Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-31T02:24:52.246Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-31T02:24:52.256Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-31T02:24:52.264Z"}
{"level":"info","message":"Secret transformed from data to stringData","service":"sealify","timestamp":"2025-07-31T02:24:54.642Z"}
{"level":"info","message":"Cluster added: apps-country-k8s-pdc-nonprod-green.hcnet.vn (method: content)","service":"sealify","timestamp":"2025-07-31T02:24:57.049Z"}
{"level":"info","message":"Convert request - clusterId: ef36689b-0716-44ce-9fe7-46de5e484589, targetNamespace: 'default'","service":"sealify","timestamp":"2025-07-31T02:25:09.851Z"}
{"level":"info","message":"Namespace resolution - from secret: 'sealify', targetNamespace: 'default', final: 'sealify'","service":"sealify","timestamp":"2025-07-31T02:25:09.869Z"}
{"level":"info","message":"Cleaned sealed secret YAML (removed creationTimestamp)","service":"sealify","timestamp":"2025-07-31T02:25:10.153Z"}
{"level":"info","message":"Secret converted for cluster: apps-country-k8s-pdc-nonprod-green.hcnet.vn","service":"sealify","timestamp":"2025-07-31T02:25:10.153Z"}
{"level":"info","message":"Secret transformed from stringData to data","service":"sealify","timestamp":"2025-07-31T02:25:19.467Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-31T02:27:01.638Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-31T02:27:01.647Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-31T02:27:01.655Z"}
{"level":"info","message":"Secret transformed from data to stringData","service":"sealify","timestamp":"2025-07-31T02:27:09.968Z"}
{"level":"info","message":"Secret transformed from data to stringData","service":"sealify","timestamp":"2025-07-31T02:29:53.608Z"}
{"level":"info","message":"Convert request - clusterId: 2fa14f29-07c0-429c-a33e-97f7e9c8c1a1, targetNamespace: 'default'","service":"sealify","timestamp":"2025-07-31T02:30:13.045Z"}
{"level":"info","message":"Namespace resolution - from secret: 'nprod-green-cnpg-tst', targetNamespace: 'default', final: 'nprod-green-cnpg-tst'","service":"sealify","timestamp":"2025-07-31T02:30:13.150Z"}
{"level":"info","message":"Cleaned sealed secret YAML (removed creationTimestamp)","service":"sealify","timestamp":"2025-07-31T02:30:13.169Z"}
{"level":"info","message":"Secret converted for cluster: db-country-k8s-pdc-nonprod-green.hcnet.vn","service":"sealify","timestamp":"2025-07-31T02:30:13.169Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-31T02:30:52.795Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-31T02:30:52.804Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-31T02:30:52.811Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-31T02:31:25.474Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-31T02:31:25.483Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-31T02:31:25.491Z"}
{"level":"info","message":"Secret transformed from data to stringData","service":"sealify","timestamp":"2025-07-31T02:31:27.218Z"}
{"level":"info","message":"Secret transformed from data to stringData","service":"sealify","timestamp":"2025-07-31T02:31:30.347Z"}
{"level":"info","message":"Secret transformed from stringData to data","service":"sealify","timestamp":"2025-07-31T02:31:41.009Z"}
{"level":"info","message":"Secret transformed from stringData to data","service":"sealify","timestamp":"2025-07-31T02:32:37.923Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-31T02:42:59.989Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-31T02:42:59.998Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-31T02:43:00.022Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-31T02:43:50.482Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-31T02:43:50.492Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-31T02:43:50.500Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-31T02:44:03.743Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-31T02:44:03.753Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-31T02:44:03.761Z"}
{"level":"info","message":"Secret transformed from data to stringData","service":"sealify","timestamp":"2025-07-31T02:44:06.535Z"}
{"level":"info","message":"Secret transformed from stringData to data","service":"sealify","timestamp":"2025-07-31T02:44:23.950Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-31T02:47:06.199Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-31T02:47:06.208Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-31T02:47:06.215Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-31T02:47:17.763Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-31T02:47:17.772Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-31T02:47:17.781Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-31T02:48:45.430Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-31T02:48:45.439Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-31T02:48:45.448Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-31T14:45:34.482Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-31T14:45:34.498Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-31T14:45:34.505Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-31T14:50:57.638Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-31T14:50:57.648Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-31T14:50:57.656Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-31T15:14:09.846Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-31T15:14:09.866Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-31T15:14:09.876Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-31T15:26:25.819Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-31T15:26:25.829Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-31T15:26:25.837Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-31T15:48:32.977Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-31T15:48:32.986Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-31T15:48:32.993Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-07-31T15:55:06.720Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-07-31T15:55:06.729Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-07-31T15:55:06.737Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-08-01T15:22:11.716Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-08-01T15:22:11.725Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-08-01T15:22:11.733Z"}
{"level":"info","message":"Sealify server running on port 3000","service":"sealify","timestamp":"2025-08-01T15:55:26.746Z"}
{"level":"info","message":"Kubeseal is installed","service":"sealify","timestamp":"2025-08-01T15:55:26.756Z"}
{"level":"info","message":"Automatic note cleanup initialized (runs every 5 minutes)","service":"sealify","timestamp":"2025-08-01T15:55:26.767Z"}
